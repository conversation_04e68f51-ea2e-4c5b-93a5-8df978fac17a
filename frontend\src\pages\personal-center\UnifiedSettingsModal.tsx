import { SettingOutlined, TeamOutlined, UserOutlined } from '@ant-design/icons';
import {
  Avatar,
  Form,
  Input,
  Modal,
  Space,
  Tabs,
  Typography,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import type { UserProfileDetailResponse } from '@/types/user';

const { Title, Text } = Typography;

interface UnifiedSettingsModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
  userInfo: UserProfileDetailResponse;
}

/**
 * 统一设置Modal组件
 *
 * 提供个人信息修改和新建团队功能的统一界面。
 * 使用Tab页面结构组织不同的设置功能。
 *
 * 主要功能：
 * 1. 个人信息修改Tab：编辑用户基本信息
 * 2. 新建团队Tab：创建新团队
 *
 * Props:
 * - visible: 控制Modal显示/隐藏
 * - onCancel: 取消操作回调
 * - onSuccess: 操作成功回调
 * - userInfo: 当前用户信息
 */
const UnifiedSettingsModal: React.FC<UnifiedSettingsModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  userInfo,
}) => {
  const [personalForm] = Form.useForm();
  const [teamForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('personal');

  // 当Modal打开时，填充个人信息表单数据
  useEffect(() => {
    if (visible && userInfo) {
      personalForm.setFieldsValue({
        name: userInfo.name,
        email: userInfo.email,
        telephone: userInfo.telephone,
        position: userInfo.position,
      });
    }
  }, [visible, userInfo, personalForm]);

  // 处理个人信息提交
  const handlePersonalSubmit = async () => {
    try {
      const values = await personalForm.validateFields();
      console.log('更新个人信息:', values);
      
      // TODO: 调用更新用户信息的API
      // await UserService.updateUserProfile(values);
      
      message.success('个人信息更新成功！');
      onSuccess?.();
      onCancel();
    } catch (error) {
      console.error('更新个人信息失败:', error);
      message.error('更新个人信息失败，请稍后重试');
    }
  };

  // 处理团队创建提交
  const handleTeamSubmit = async () => {
    try {
      const values = await teamForm.validateFields();
      console.log('创建团队:', values);
      
      // TODO: 调用创建团队的API
      // await TeamService.createTeam(values);
      
      message.success('团队创建成功！');
      teamForm.resetFields();
      onSuccess?.();
      onCancel();
    } catch (error) {
      console.error('创建团队失败:', error);
      message.error('创建团队失败，请稍后重试');
    }
  };

  // 处理取消操作
  const handleCancel = () => {
    personalForm.resetFields();
    teamForm.resetFields();
    setActiveTab('personal');
    onCancel();
  };

  // 根据当前Tab决定提交操作
  const handleOk = () => {
    if (activeTab === 'personal') {
      handlePersonalSubmit();
    } else {
      handleTeamSubmit();
    }
  };

  return (
    <Modal
      title={
        <Space align="center">
          <SettingOutlined style={{ fontSize: 18, color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0, fontSize: 16 }}>
            设置
          </Title>
        </Space>
      }
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText={activeTab === 'personal' ? '保存设置' : '创建团队'}
      cancelText="取消"
      width={480}
      destroyOnClose
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: 16,
        },
        body: {
          padding: '20px',
        },
      }}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        style={{ marginTop: -8 }}
        items={[
          {
            key: 'personal',
            label: (
              <Space align="center">
                <UserOutlined />
                <span>个人信息</span>
              </Space>
            ),
            children: (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <Text style={{ color: '#8c8c8c', fontSize: 14 }}>
                    编辑您的个人信息和偏好设置
                  </Text>
                </div>

                <Form
                  form={personalForm}
                  layout="vertical"
                  requiredMark={false}
                  autoComplete="off"
                >
                  {/* 姓名 */}
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 600, fontSize: 14 }}>
                        姓名
                      </Text>
                    }
                    name="name"
                    rules={[
                      { required: true, message: '请输入姓名' },
                      { min: 2, message: '姓名至少2个字符' },
                      { max: 20, message: '姓名不能超过20个字符' },
                    ]}
                  >
                    <Input
                      placeholder="请输入姓名"
                      style={{
                        borderRadius: 6,
                        fontSize: 14,
                      }}
                    />
                  </Form.Item>

                  {/* 职位 */}
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 600, fontSize: 14 }}>
                        职位
                      </Text>
                    }
                    name="position"
                    rules={[
                      { max: 50, message: '职位不能超过50个字符' },
                    ]}
                  >
                    <Input
                      placeholder="请输入职位（可选）"
                      style={{
                        borderRadius: 6,
                        fontSize: 14,
                      }}
                    />
                  </Form.Item>

                  {/* 邮箱 */}
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 600, fontSize: 14 }}>
                        邮箱
                      </Text>
                    }
                    name="email"
                    rules={[
                      { required: true, message: '请输入邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' },
                    ]}
                  >
                    <Input
                      placeholder="请输入邮箱"
                      style={{
                        borderRadius: 6,
                        fontSize: 14,
                      }}
                    />
                  </Form.Item>

                  {/* 电话 */}
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 600, fontSize: 14 }}>
                        电话
                      </Text>
                    }
                    name="telephone"
                    rules={[
                      { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' },
                    ]}
                  >
                    <Input
                      placeholder="请输入电话（可选）"
                      style={{
                        borderRadius: 6,
                        fontSize: 14,
                      }}
                    />
                  </Form.Item>
                </Form>
              </div>
            ),
          },
          {
            key: 'team',
            label: (
              <Space align="center">
                <TeamOutlined />
                <span>新建团队</span>
              </Space>
            ),
            children: (
              <div>
                <div style={{ marginBottom: 24, textAlign: 'center' }}>
                  <Text style={{ color: '#8c8c8c', fontSize: 14, lineHeight: 1.6 }}>
                    创建一个新的团队来协作管理项目和任务
                  </Text>
                </div>

                <Form
                  form={teamForm}
                  layout="vertical"
                  requiredMark={false}
                  autoComplete="off"
                >
                  {/* 团队名称 */}
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 600, fontSize: 15 }}>
                        团队名称
                      </Text>
                    }
                    name="teamName"
                    rules={[
                      { required: true, message: '请输入团队名称' },
                      { min: 2, message: '团队名称至少2个字符' },
                      { max: 50, message: '团队名称不能超过50个字符' },
                    ]}
                    style={{ marginBottom: 0 }}
                  >
                    <Input
                      placeholder="请输入团队名称"
                      size="large"
                      style={{
                        borderRadius: 8,
                        fontSize: 15,
                        padding: '12px 16px',
                        border: '2px solid #d9d9d9',
                        transition: 'all 0.3s ease',
                      }}
                      onFocus={(e) => {
                        e.target.style.borderColor = '#1890ff';
                        e.target.style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.1)';
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = '#d9d9d9';
                        e.target.style.boxShadow = 'none';
                      }}
                    />
                  </Form.Item>
                </Form>
              </div>
            ),
          },
        ]}
      />
    </Modal>
  );
};

export default UnifiedSettingsModal;
