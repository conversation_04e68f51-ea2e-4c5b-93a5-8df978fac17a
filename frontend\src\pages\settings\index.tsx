import { useModel, history } from '@umijs/max';
import { Card, Col, Row, Spin, Typography, Divider, Button, Space } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import React, { useEffect } from 'react';
import UserFloatButton from '@/components/FloatButton';
import TeamManagementCard from './TeamManagementCard';
import UserProfileCard from './UserProfileCard';

const { Title, Text } = Typography;

/**
 * 设置页面组件
 *
 * 这是用户的设置主页面，提供用户个人设置、团队管理等功能。
 * 是用户进行各种配置和管理操作的主要入口页面。
 *
 * 页面功能：
 * 1. 用户个人信息设置
 * 2. 团队创建和管理
 * 3. 账户设置和偏好配置
 * 4. 订阅和计费管理
 *
 * 页面结构：
 * - 顶部：页面标题和描述
 * - 左侧：用户个人设置（响应式布局）
 * - 右侧：团队管理设置（响应式布局）
 * - 浮动：全局操作按钮
 *
 * 权限控制：
 * - 需要用户登录才能访问
 * - 由应用级路由守卫处理登录检查
 * - 支持登录状态变化的实时响应
 *
 * 响应式设计：
 * - 移动端：垂直堆叠布局
 * - 桌面端：左右分栏布局
 * - 自适应不同屏幕尺寸
 */
const SettingsPage: React.FC = () => {
  /**
   * 全局状态管理
   *
   * 从UmiJS全局状态中获取用户信息和加载状态：
   * - initialState: 包含用户和团队信息的全局状态
   * - loading: 全局状态的加载状态
   */
  const { initialState, loading } = useModel('@@initialState');

  /**
   * 返回上一页
   *
   * 在UmiJS 4中，history.goBack()方法已被移除，使用history.go(-1)替代。
   * 如果没有历史记录可以返回，则跳转到仪表盘作为默认页面。
   */
  const handleGoBack = () => {
    // 检查是否有历史记录可以返回
    if (window.history.length > 1) {
      history.go(-1);
    } else {
      // 如果没有历史记录，跳转到仪表盘
      history.push('/dashboard');
    }
  };

  /**
   * 加载状态处理
   *
   * 当全局状态正在初始化时，显示加载界面。
   * 这确保了用户在状态加载完成前看到友好的加载提示。
   */
  if (loading) {
    return (
      <div
        style={{
          minHeight: '100vh',
          background: '#f5f8ff',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Spin size="large" />
        <div style={{ marginLeft: 16 }}>正在加载设置信息...</div>
      </div>
    );
  }

  return (
    <>
      {/* 页面主容器 */}
      <div
        style={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #f5f8ff 0%, #e8f4fd 100%)',
          padding: '24px',
        }}
      >
        {/* 页面标题区域 */}
        <div style={{ marginBottom: 24 }}>
          {/* 返回按钮 */}
          <div style={{ marginBottom: 16 }}>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleGoBack}
              style={{
                fontSize: '16px',
                color: '#1890ff',
                padding: '4px 8px',
              }}
            >
              返回
            </Button>
          </div>

          {/* 标题 */}
          <div style={{ textAlign: 'center' }}>
            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
              设置中心
            </Title>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              管理您的个人信息、团队设置和账户偏好
            </Text>
          </div>
        </div>

        <Card
          style={{
            maxWidth: 1400,
            margin: '0 auto',
            borderRadius: 16,
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
            border: 'none',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
          }}
          bodyStyle={{
            padding: '32px',
          }}
        >
          {/*
           * 响应式网格布局
           *
           * 使用Ant Design的Row/Col组件实现响应式布局：
           * - 移动端：垂直堆叠，所有组件占满宽度
           * - 桌面端：个人设置和团队管理左右分栏
           * - gutter: 组件间距设置
           * - margin: 0: 避免Row组件的默认负边距影响布局
           */}
          <Row gutter={[24, 24]} style={{ margin: 0 }}>
            {/*
             * 用户个人设置区域
             *
             * 显示用户的个人信息设置、账户配置等。
             * 响应式布局：
             * - 移动端(xs-md)：全宽显示
             * - 桌面端(lg+)：占据左半部分
             */}
            <Col
              xs={24}
              sm={24}
              md={24}
              lg={12}
              xl={12}
              xxl={12}
              style={{ marginBottom: 8 }}
            >
              <UserProfileCard />
            </Col>

            {/*
             * 团队管理设置区域
             *
             * 显示团队创建、团队管理等功能。
             * 这是团队管理功能的主要入口。
             * 响应式布局：
             * - 移动端(xs-md)：全宽显示
             * - 桌面端(lg+)：占据右半部分
             */}
            <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
              <TeamManagementCard />
            </Col>
          </Row>
        </Card>
      </div>

      {/* 全局浮动操作按钮 */}
      <UserFloatButton />
    </>
  );
};

export default SettingsPage;
