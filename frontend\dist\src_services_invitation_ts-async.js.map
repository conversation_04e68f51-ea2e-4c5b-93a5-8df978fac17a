{"version": 3, "sources": ["src/services/invitation.ts"], "sourcesContent": ["/**\n * 团队邀请相关 API 服务\n */\n\nimport type {\n  TeamInvitationResponse,\n  RespondInvitationRequest,\n  SendInvitationResponse,\n  AcceptInvitationByLinkRequest,\n  AcceptInvitationByLinkResponse,\n  InvitationInfoResponse,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\n/**\n * 邀请服务类\n */\nexport class InvitationService {\n  /**\n   * 获取当前团队的邀请列表（需要 Team Token，仅创建者）\n   */\n  static async getCurrentTeamInvitations(): Promise<TeamInvitationResponse[]> {\n    const response = await apiRequest.get<TeamInvitationResponse[]>('/teams/current/invitations');\n    return response.data;\n  }\n\n  /**\n   * 获取用户收到的邀请列表（需要 Account Token）\n   */\n  static async getUserReceivedInvitations(): Promise<TeamInvitationResponse[]> {\n    const response = await apiRequest.get<TeamInvitationResponse[]>('/invitations/user/received');\n    return response.data;\n  }\n\n  /**\n   * 获取用户收到的待处理邀请列表（需要 Account Token）\n   */\n  static async getUserPendingInvitations(): Promise<TeamInvitationResponse[]> {\n    const response = await apiRequest.get<TeamInvitationResponse[]>('/invitations/user/pending');\n    return response.data;\n  }\n\n  /**\n   * 响应邀请（需要 Account Token）\n   */\n  static async respondToInvitation(\n    invitationId: number,\n    data: RespondInvitationRequest,\n  ): Promise<void> {\n    await apiRequest.post<void>(`/invitations/${invitationId}/respond`, data);\n  }\n\n  /**\n   * 取消邀请（需要 Team Token，仅邀请人）\n   */\n  static async cancelInvitation(invitationId: number): Promise<void> {\n    await apiRequest.delete<void>(`/invitations/${invitationId}`);\n  }\n\n  /**\n   * 获取邀请详情\n   */\n  static async getInvitationDetail(invitationId: number): Promise<TeamInvitationResponse> {\n    const response = await apiRequest.get<TeamInvitationResponse>(`/invitations/${invitationId}`);\n    return response.data;\n  }\n\n  /**\n   * 发送邀请并生成邀请链接（需要 Team Token，仅创建者）\n   */\n  static async sendInvitations(data: { emails: string[]; message?: string }): Promise<SendInvitationResponse> {\n    const response = await apiRequest.post<SendInvitationResponse>('/invitations/send', data);\n    return response.data;\n  }\n\n  /**\n   * 获取邀请信息（公开接口）\n   */\n  static async getInvitationInfo(token: string): Promise<InvitationInfoResponse> {\n    const response = await apiRequest.get<InvitationInfoResponse>(\n      `/invitations/info/${token}`\n    );\n    return response.data;\n  }\n\n  /**\n   * 通过邀请链接接受邀请（公开接口）\n   */\n  static async acceptInvitationByLink(\n    token: string,\n    data: AcceptInvitationByLinkRequest\n  ): Promise<AcceptInvitationByLinkResponse> {\n    const response = await apiRequest.post<AcceptInvitationByLinkResponse>(\n      `/invitations/accept-by-link/${token}`,\n      data\n    );\n    return response.data;\n  }\n\n  /**\n   * 更新过期邀请状态（系统内部接口）\n   */\n  static async updateExpiredInvitations(): Promise<number> {\n    const response = await apiRequest.post<number>('/invitations/system/update-expired');\n    return response.data;\n  }\n}\n\n// 默认导出\nexport default InvitationService;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;;;;;;;;IAeY,iBAAiB;eAAjB;;IA2Fb,OAAO;IACP,OAAiC;eAAjC;;;;;gCAjG2B;;;;;;;;;AAKpB,MAAM;IACX;;GAEC,GACD,aAAa,4BAA+D;QAC1E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA2B;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,6BAAgE;QAC3E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA2B;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,4BAA+D;QAC1E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA2B;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,oBACX,YAAoB,EACpB,IAA8B,EACf;QACf,MAAM,mBAAU,CAAC,IAAI,CAAO,CAAC,aAAa,EAAE,aAAa,QAAQ,CAAC,EAAE;IACtE;IAEA;;GAEC,GACD,aAAa,iBAAiB,YAAoB,EAAiB;QACjE,MAAM,mBAAU,CAAC,MAAM,CAAO,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9D;IAEA;;GAEC,GACD,aAAa,oBAAoB,YAAoB,EAAmC;QACtF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAyB,CAAC,aAAa,EAAE,aAAa,CAAC;QAC5F,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,gBAAgB,IAA4C,EAAmC;QAC1G,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAyB,qBAAqB;QACpF,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,kBAAkB,KAAa,EAAmC;QAC7E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,CAAC,kBAAkB,EAAE,MAAM,CAAC;QAE9B,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,uBACX,KAAa,EACb,IAAmC,EACM;QACzC,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,4BAA4B,EAAE,MAAM,CAAC,EACtC;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,2BAA4C;QACvD,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAS;QAC/C,OAAO,SAAS,IAAI;IACtB;AACF;IAGA,WAAe"}