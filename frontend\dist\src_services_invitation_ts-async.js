((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['src/services/invitation.ts'],
{ "src/services/invitation.ts": function (module, exports, __mako_require__){
/**
 * 团队邀请相关 API 服务
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    InvitationService: function() {
        return InvitationService;
    },
    // 默认导出
    default: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
class InvitationService {
    /**
   * 获取当前团队的邀请列表（需要 Team Token，仅创建者）
   */ static async getCurrentTeamInvitations() {
        const response = await _request.apiRequest.get('/teams/current/invitations');
        return response.data;
    }
    /**
   * 获取用户收到的邀请列表（需要 Account Token）
   */ static async getUserReceivedInvitations() {
        const response = await _request.apiRequest.get('/invitations/user/received');
        return response.data;
    }
    /**
   * 获取用户收到的待处理邀请列表（需要 Account Token）
   */ static async getUserPendingInvitations() {
        const response = await _request.apiRequest.get('/invitations/user/pending');
        return response.data;
    }
    /**
   * 响应邀请（需要 Account Token）
   */ static async respondToInvitation(invitationId, data) {
        await _request.apiRequest.post(`/invitations/${invitationId}/respond`, data);
    }
    /**
   * 取消邀请（需要 Team Token，仅邀请人）
   */ static async cancelInvitation(invitationId) {
        await _request.apiRequest.delete(`/invitations/${invitationId}`);
    }
    /**
   * 获取邀请详情
   */ static async getInvitationDetail(invitationId) {
        const response = await _request.apiRequest.get(`/invitations/${invitationId}`);
        return response.data;
    }
    /**
   * 发送邀请并生成邀请链接（需要 Team Token，仅创建者）
   */ static async sendInvitations(data) {
        const response = await _request.apiRequest.post('/invitations/send', data);
        return response.data;
    }
    /**
   * 获取邀请信息（公开接口）
   */ static async getInvitationInfo(token) {
        const response = await _request.apiRequest.get(`/invitations/info/${token}`);
        return response.data;
    }
    /**
   * 通过邀请链接接受邀请（公开接口）
   */ static async acceptInvitationByLink(token, data) {
        const response = await _request.apiRequest.post(`/invitations/accept-by-link/${token}`, data);
        return response.data;
    }
    /**
   * 更新过期邀请状态（系统内部接口）
   */ static async updateExpiredInvitations() {
        const response = await _request.apiRequest.post('/invitations/system/update-expired');
        return response.data;
    }
}
var _default = InvitationService;
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_services_invitation_ts-async.js.map