import { useModel } from '@umijs/max';
import {
  Card,
  Form,
  Input,
  Button,
  Typography,
  Space,
  Divider,
  message,
  Avatar,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { UserService } from '@/services';
import type { UpdateUserProfileRequest } from '@/types/api';

const { Title, Text } = Typography;

/**
 * 用户个人设置卡片组件
 *
 * 提供用户个人信息的查看和编辑功能，包括：
 * - 用户名修改
 * - 联系电话设置
 * - 头像显示（邮箱不可修改）
 * - 个人资料保存
 *
 * 功能特点：
 * - 实时表单验证
 * - 自动保存状态反馈
 * - 响应式设计
 * - 错误处理
 */
const UserProfileCard: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { initialState, setInitialState } = useModel('@@initialState');

  /**
   * 初始化表单数据
   *
   * 当用户信息加载完成后，自动填充表单字段
   */
  useEffect(() => {
    if (initialState?.currentUser) {
      form.setFieldsValue({
        name: initialState.currentUser.name,
        email: initialState.currentUser.email,
        telephone: initialState.currentUser.telephone || '',
      });
    }
  }, [initialState?.currentUser, form]);

  /**
   * 保存用户个人资料
   *
   * 处理用户个人信息的更新，包括：
   * 1. 表单验证
   * 2. API调用
   * 3. 全局状态更新
   * 4. 用户反馈
   */
  const handleSaveProfile = async (values: UpdateUserProfileRequest) => {
    setLoading(true);
    try {
      // 调用用户信息更新API
      const updatedUser = await UserService.updateProfile(values);

      // 更新全局状态中的用户信息
      await setInitialState((prevState) => ({
        ...prevState,
        currentUser: updatedUser,
      }));

      message.success('个人资料更新成功');
    } catch (error) {
      // 错误处理由响应拦截器统一处理
      console.error('更新个人资料失败:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取用户头像显示
   *
   * 根据用户名生成头像显示，如果没有用户名则使用邮箱前缀
   */
  const getUserAvatar = () => {
    const user = initialState?.currentUser;
    if (!user) return 'U';
    
    return user.name ? user.name.charAt(0).toUpperCase() : 
           user.email ? user.email.charAt(0).toUpperCase() : 'U';
  };

  return (
    <Card
      className="dashboard-card"
      style={{
        borderRadius: 16,
        boxShadow: '0 6px 20px rgba(0,0,0,0.08)',
        border: 'none',
        background: 'linear-gradient(145deg, #ffffff, #f8faff)',
        height: '100%',
      }}
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <Avatar
            size={40}
            style={{
              backgroundColor: '#1890ff',
              fontSize: '18px',
              fontWeight: 'bold',
            }}
            icon={<UserOutlined />}
          >
            {getUserAvatar()}
          </Avatar>
          <div>
            <Title
              level={4}
              style={{
                margin: 0,
                background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: 600,
              }}
            >
              个人资料
            </Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              管理您的个人信息和联系方式
            </Text>
          </div>
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSaveProfile}
        autoComplete="off"
        style={{ marginTop: 16 }}
      >
        <Form.Item
          label="用户名"
          name="name"
          rules={[
            { required: true, message: '请输入用户名' },
            { max: 100, message: '用户名不能超过100个字符' },
            { min: 2, message: '用户名至少需要2个字符' },
          ]}
        >
          <Input
            prefix={<UserOutlined style={{ color: '#1890ff' }} />}
            placeholder="请输入用户名"
            size="large"
          />
        </Form.Item>

        <Form.Item label="邮箱地址" name="email">
          <Input
            prefix={<MailOutlined style={{ color: '#1890ff' }} />}
            disabled
            placeholder="邮箱地址不可修改"
            size="large"
            style={{ backgroundColor: '#f5f5f5' }}
          />
        </Form.Item>

        <Form.Item
          label="联系电话"
          name="telephone"
          rules={[
            { max: 20, message: '联系电话不能超过20个字符' },
            {
              pattern: /^[0-9+\-\s()]*$/,
              message: '请输入有效的电话号码',
            },
          ]}
        >
          <Input
            prefix={<PhoneOutlined style={{ color: '#1890ff' }} />}
            placeholder="请输入联系电话（可选）"
            size="large"
          />
        </Form.Item>

        <Divider style={{ margin: '24px 0' }} />

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={<SaveOutlined />}
            size="large"
            style={{
              borderRadius: 8,
              background: 'linear-gradient(135deg, #1890ff, #722ed1)',
              border: 'none',
              boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',
              minWidth: 120,
            }}
          >
            保存设置
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default UserProfileCard;
