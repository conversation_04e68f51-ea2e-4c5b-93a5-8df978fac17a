{"version": 3, "sources": ["src/pages/settings/TeamManagementCard.tsx", "src/pages/settings/UserProfileCard.tsx", "src/pages/settings/index.tsx"], "sourcesContent": ["import { useModel, history } from '@umijs/max';\nimport {\n  Card,\n  Form,\n  Input,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Modal,\n  List,\n  Tag,\n  Flex,\n  Popconfirm,\n  Dropdown,\n} from 'antd';\nimport {\n  TeamOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  LogoutOutlined,\n  MoreOutlined,\n} from '@ant-design/icons';\nimport React, { useState, useEffect } from 'react';\nimport { TeamService, AuthService } from '@/services';\nimport type { CreateTeamRequest, TeamDetailResponse } from '@/types/api';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\n/**\n * 团队管理设置卡片组件\n *\n * 提供团队相关的管理功能，包括：\n * - 创建新团队\n * - 查看用户的团队列表\n * - 团队快速操作\n * - 团队设置入口\n *\n * 功能特点：\n * - 创建团队模态框\n * - 团队列表展示\n * - 快速导航到团队管理\n * - 响应式设计\n */\nconst TeamManagementCard: React.FC = () => {\n  const [form] = Form.useForm();\n  const [editForm] = Form.useForm();\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [createLoading, setCreateLoading] = useState(false);\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [teamsLoading, setTeamsLoading] = useState(false);\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [editingTeam, setEditingTeam] = useState<TeamDetailResponse | null>(null);\n  const [editLoading, setEditLoading] = useState(false);\n  const [operationLoading, setOperationLoading] = useState<{[key: number]: boolean}>({});\n  const { initialState, setInitialState } = useModel('@@initialState');\n\n  /**\n   * 获取用户团队列表\n   *\n   * 在组件挂载时获取用户的团队列表\n   */\n  useEffect(() => {\n    if (initialState?.currentUser) {\n      fetchTeams();\n    }\n  }, [initialState?.currentUser]);\n\n  /**\n   * 获取团队列表数据\n   */\n  const fetchTeams = async () => {\n    setTeamsLoading(true);\n    try {\n      const teamsData = await TeamService.getUserTeamsWithStats();\n      setTeams(teamsData);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n    } finally {\n      setTeamsLoading(false);\n    }\n  };\n\n  /**\n   * 创建团队处理函数\n   *\n   * 处理团队创建的完整流程：\n   * 1. 表单验证\n   * 2. API调用\n   * 3. 更新团队列表\n   * 4. 关闭模态框\n   * 5. 用户反馈\n   */\n  const handleCreateTeam = async (values: CreateTeamRequest) => {\n    setCreateLoading(true);\n    try {\n      await TeamService.createTeam(values);\n\n      // 重新获取团队列表\n      await fetchTeams();\n\n      // 关闭模态框并重置表单\n      setCreateModalVisible(false);\n      form.resetFields();\n\n      message.success('团队创建成功！');\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n      console.error('创建团队失败:', error);\n    } finally {\n      setCreateLoading(false);\n    }\n  };\n\n  /**\n   * 编辑团队名称\n   */\n  const handleEditTeam = (team: TeamDetailResponse) => {\n    setEditingTeam(team);\n    editForm.setFieldsValue({\n      name: team.name,\n      description: team.description,\n    });\n    setEditModalVisible(true);\n  };\n\n  /**\n   * 保存团队编辑\n   */\n  const handleSaveTeamEdit = async (values: any) => {\n    if (!editingTeam) return;\n\n    setEditLoading(true);\n    try {\n      // 先切换到目标团队\n      await AuthService.selectTeam({ teamId: editingTeam.id });\n\n      // 更新团队信息\n      await TeamService.updateCurrentTeam({\n        name: values.name,\n        description: values.description,\n      });\n\n      // 重新获取团队列表\n      await fetchTeams();\n\n      // 关闭模态框\n      setEditModalVisible(false);\n      setEditingTeam(null);\n      editForm.resetFields();\n\n      message.success('团队信息更新成功！');\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setEditLoading(false);\n    }\n  };\n\n  /**\n   * 删除团队\n   */\n  const handleDeleteTeam = async (team: TeamDetailResponse) => {\n    setOperationLoading(prev => ({ ...prev, [team.id]: true }));\n    try {\n      await TeamService.deleteTeam(team.id);\n\n      // 重新获取团队列表\n      await fetchTeams();\n\n      message.success('团队删除成功！');\n    } catch (error) {\n      console.error('删除团队失败:', error);\n      message.error('删除团队失败');\n    } finally {\n      setOperationLoading(prev => ({ ...prev, [team.id]: false }));\n    }\n  };\n\n  /**\n   * 退出团队\n   */\n  const handleLeaveTeam = async (team: TeamDetailResponse) => {\n    setOperationLoading(prev => ({ ...prev, [team.id]: true }));\n    try {\n      await TeamService.leaveTeam();\n\n      // 重新获取团队列表\n      await fetchTeams();\n\n      // 更新全局状态，清除当前团队\n      if (setInitialState) {\n        await setInitialState((prevState) => ({\n          ...prevState,\n          currentTeam: undefined,\n        }));\n      }\n\n      message.success('已退出团队！');\n    } catch (error) {\n      console.error('退出团队失败:', error);\n      message.error('退出团队失败');\n    } finally {\n      setOperationLoading(prev => ({ ...prev, [team.id]: false }));\n    }\n  };\n\n\n\n  return (\n    <>\n      <Card\n        className=\"dashboard-card\"\n        style={{\n          borderRadius: 16,\n          boxShadow: '0 6px 20px rgba(0,0,0,0.08)',\n          border: 'none',\n          background: 'linear-gradient(145deg, #ffffff, #f8faff)',\n          height: '100%',\n        }}\n        title={\n          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>\n            <TeamOutlined\n              style={{\n                fontSize: '24px',\n                color: '#1890ff',\n                padding: '8px',\n                backgroundColor: '#e6f7ff',\n                borderRadius: '8px',\n              }}\n            />\n            <div>\n              <Title\n                level={4}\n                style={{\n                  margin: 0,\n                  background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  fontWeight: 600,\n                }}\n              >\n                团队管理\n              </Title>\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                创建和管理您的团队\n              </Text>\n            </div>\n          </div>\n        }\n      >\n        {/* 创建团队按钮 */}\n        <div style={{ marginBottom: 24 }}>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => setCreateModalVisible(true)}\n            size=\"large\"\n            block\n            style={{\n              borderRadius: 8,\n              background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n              border: 'none',\n              boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',\n              height: '48px',\n              fontSize: '16px',\n              fontWeight: 600,\n            }}\n          >\n            创建新团队\n          </Button>\n        </div>\n\n        <Divider style={{ margin: '24px 0' }}>\n          <Text type=\"secondary\">我的团队</Text>\n        </Divider>\n\n        {/* 团队列表 */}\n        <List\n          loading={teamsLoading}\n          dataSource={teams} // 显示所有团队\n          renderItem={(team) => {\n            const getTeamActions = (team: TeamDetailResponse) => {\n              if (team.isCreator) {\n                return [\n                  {\n                    key: 'edit',\n                    label: '编辑团队名称',\n                    icon: <EditOutlined />,\n                    onClick: () => handleEditTeam(team),\n                  },\n                  {\n                    key: 'delete',\n                    label: (\n                      <Popconfirm\n                        title=\"删除团队\"\n                        description={`确定要删除团队\"${team.name}\"吗？此操作不可恢复！`}\n                        onConfirm={() => handleDeleteTeam(team)}\n                        okText=\"确定删除\"\n                        cancelText=\"取消\"\n                        okButtonProps={{ danger: true }}\n                      >\n                        <span style={{ color: '#ff4d4f' }}>删除团队</span>\n                      </Popconfirm>\n                    ),\n                    icon: <DeleteOutlined />,\n                    danger: true,\n                  },\n                ];\n              } else {\n                return [\n                  {\n                    key: 'leave',\n                    label: (\n                      <Popconfirm\n                        title=\"退出团队\"\n                        description={`确定要退出团队\"${team.name}\"吗？`}\n                        onConfirm={() => handleLeaveTeam(team)}\n                        okText=\"确定退出\"\n                        cancelText=\"取消\"\n                        okButtonProps={{ danger: true }}\n                      >\n                        <span style={{ color: '#ff4d4f' }}>退出团队</span>\n                      </Popconfirm>\n                    ),\n                    icon: <LogoutOutlined />,\n                    danger: true,\n                  },\n                ];\n              }\n            };\n\n            return (\n              <List.Item\n                style={{\n                  padding: '12px 0',\n                  borderBottom: '1px solid #f0f0f0',\n                }}\n                actions={[\n                  <Dropdown\n                    key=\"actions\"\n                    menu={{\n                      items: getTeamActions(team),\n                    }}\n                    trigger={['click']}\n                    placement=\"bottomRight\"\n                  >\n                    <Button\n                      type=\"text\"\n                      icon={<MoreOutlined />}\n                      loading={operationLoading[team.id]}\n                      style={{ fontSize: '16px' }}\n                    />\n                  </Dropdown>\n                ]}\n              >\n                <List.Item.Meta\n                  avatar={\n                    <div\n                      style={{\n                        width: 40,\n                        height: 40,\n                        borderRadius: 8,\n                        background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        color: 'white',\n                        fontWeight: 'bold',\n                        fontSize: '16px',\n                      }}\n                    >\n                      {team.name.charAt(0).toUpperCase()}\n                    </div>\n                  }\n                  title={\n                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n                      <Text strong style={{ fontSize: '14px' }}>\n                        {team.name}\n                      </Text>\n                      {team.isCreator && (\n                        <Tag color=\"blue\" style={{ fontSize: '10px' }}>\n                          创建者\n                        </Tag>\n                      )}\n                    </div>\n                  }\n                  description={\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                      {team.memberCount} 名成员\n                    </Text>\n                  }\n                />\n              </List.Item>\n            );\n          }}\n          locale={{ emptyText: '暂无团队，创建您的第一个团队吧！' }}\n        />\n\n\n      </Card>\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => {\n          setEditModalVisible(false);\n          setEditingTeam(null);\n          editForm.resetFields();\n        }}\n        footer={null}\n        width={500}\n        style={{ top: 100 }}\n      >\n        <Form\n          form={editForm}\n          layout=\"vertical\"\n          onFinish={handleSaveTeamEdit}\n          autoComplete=\"off\"\n          style={{ marginTop: 24 }}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称！' },\n              { max: 100, message: '团队名称长度不能超过100字符！' },\n              { min: 2, message: '团队名称至少需要2个字符！' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" size=\"large\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[{ max: 500, message: '团队描述长度不能超过500字符！' }]}\n          >\n            <TextArea\n              placeholder=\"请输入团队描述（可选）\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button\n                onClick={() => {\n                  setEditModalVisible(false);\n                  setEditingTeam(null);\n                  editForm.resetFields();\n                }}\n              >\n                取消\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={editLoading}\n                icon={<EditOutlined />}\n              >\n                保存修改\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 创建团队模态框 */}\n      <Modal\n        title={\n          <Flex align=\"center\" gap={8}>\n            <TeamOutlined style={{ color: '#1890ff' }} />\n            <span>创建新团队</span>\n          </Flex>\n        }\n        open={createModalVisible}\n        onCancel={() => {\n          setCreateModalVisible(false);\n          form.resetFields();\n        }}\n        footer={null}\n        width={500}\n        style={{ top: 100 }}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleCreateTeam}\n          autoComplete=\"off\"\n          style={{ marginTop: 24 }}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称！' },\n              { max: 100, message: '团队名称长度不能超过100字符！' },\n              { min: 2, message: '团队名称至少需要2个字符！' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" size=\"large\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[{ max: 500, message: '团队描述长度不能超过500字符！' }]}\n          >\n            <TextArea\n              placeholder=\"请输入团队描述（可选）\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, marginTop: 32 }}>\n            <Flex justify=\"end\" gap={12}>\n              <Button\n                onClick={() => {\n                  setCreateModalVisible(false);\n                  form.resetFields();\n                }}\n              >\n                取消\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={createLoading}\n                style={{\n                  background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n                  border: 'none',\n                }}\n              >\n                创建团队\n              </Button>\n            </Flex>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </>\n  );\n};\n\nexport default TeamManagementCard;\n", "import { useModel } from '@umijs/max';\nimport {\n  Card,\n  Form,\n  Input,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Avatar,\n} from 'antd';\nimport {\n  UserOutlined,\n  MailOutlined,\n  PhoneOutlined,\n  SaveOutlined,\n} from '@ant-design/icons';\nimport React, { useState, useEffect } from 'react';\nimport { UserService } from '@/services';\nimport type { UpdateUserProfileRequest } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\n/**\n * 用户个人设置卡片组件\n *\n * 提供用户个人信息的查看和编辑功能，包括：\n * - 用户名修改\n * - 联系电话设置\n * - 头像显示（邮箱不可修改）\n * - 个人资料保存\n *\n * 功能特点：\n * - 实时表单验证\n * - 自动保存状态反馈\n * - 响应式设计\n * - 错误处理\n */\nconst UserProfileCard: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const { initialState, setInitialState } = useModel('@@initialState');\n\n  /**\n   * 初始化表单数据\n   *\n   * 当用户信息加载完成后，自动填充表单字段\n   */\n  useEffect(() => {\n    if (initialState?.currentUser) {\n      form.setFieldsValue({\n        name: initialState.currentUser.name,\n        email: initialState.currentUser.email,\n        telephone: initialState.currentUser.telephone || '',\n      });\n    }\n  }, [initialState?.currentUser, form]);\n\n  /**\n   * 保存用户个人资料\n   *\n   * 处理用户个人信息的更新，包括：\n   * 1. 表单验证\n   * 2. API调用\n   * 3. 全局状态更新\n   * 4. 用户反馈\n   */\n  const handleSaveProfile = async (values: UpdateUserProfileRequest) => {\n    setLoading(true);\n    try {\n      // 调用用户信息更新API\n      const updatedUser = await UserService.updateProfile(values);\n\n      // 更新全局状态中的用户信息\n      await setInitialState((prevState) => ({\n        ...prevState,\n        currentUser: updatedUser,\n      }));\n\n      message.success('个人资料更新成功');\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n      console.error('更新个人资料失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * 获取用户头像显示\n   *\n   * 根据用户名生成头像显示，如果没有用户名则使用邮箱前缀\n   */\n  const getUserAvatar = () => {\n    const user = initialState?.currentUser;\n    if (!user) return 'U';\n    \n    return user.name ? user.name.charAt(0).toUpperCase() : \n           user.email ? user.email.charAt(0).toUpperCase() : 'U';\n  };\n\n  return (\n    <Card\n      className=\"dashboard-card\"\n      style={{\n        borderRadius: 16,\n        boxShadow: '0 6px 20px rgba(0,0,0,0.08)',\n        border: 'none',\n        background: 'linear-gradient(145deg, #ffffff, #f8faff)',\n        height: '100%',\n      }}\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>\n          <Avatar\n            size={40}\n            style={{\n              backgroundColor: '#1890ff',\n              fontSize: '18px',\n              fontWeight: 'bold',\n            }}\n            icon={<UserOutlined />}\n          >\n            {getUserAvatar()}\n          </Avatar>\n          <div>\n            <Title\n              level={4}\n              style={{\n                margin: 0,\n                background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                fontWeight: 600,\n              }}\n            >\n              个人资料\n            </Title>\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n              管理您的个人信息和联系方式\n            </Text>\n          </div>\n        </div>\n      }\n    >\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleSaveProfile}\n        autoComplete=\"off\"\n        style={{ marginTop: 16 }}\n      >\n        <Form.Item\n          label=\"用户名\"\n          name=\"name\"\n          rules={[\n            { required: true, message: '请输入用户名' },\n            { max: 100, message: '用户名不能超过100个字符' },\n            { min: 2, message: '用户名至少需要2个字符' },\n          ]}\n        >\n          <Input\n            prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n            placeholder=\"请输入用户名\"\n            size=\"large\"\n          />\n        </Form.Item>\n\n        <Form.Item label=\"邮箱地址\" name=\"email\">\n          <Input\n            prefix={<MailOutlined style={{ color: '#1890ff' }} />}\n            disabled\n            placeholder=\"邮箱地址不可修改\"\n            size=\"large\"\n            style={{ backgroundColor: '#f5f5f5' }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label=\"联系电话\"\n          name=\"telephone\"\n          rules={[\n            { max: 20, message: '联系电话不能超过20个字符' },\n            {\n              pattern: /^[0-9+\\-\\s()]*$/,\n              message: '请输入有效的电话号码',\n            },\n          ]}\n        >\n          <Input\n            prefix={<PhoneOutlined style={{ color: '#1890ff' }} />}\n            placeholder=\"请输入联系电话（可选）\"\n            size=\"large\"\n          />\n        </Form.Item>\n\n        <Divider style={{ margin: '24px 0' }} />\n\n        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n            style={{\n              borderRadius: 8,\n              background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n              border: 'none',\n              boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',\n              minWidth: 120,\n            }}\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n};\n\nexport default UserProfileCard;\n", "import { useModel, history } from '@umijs/max';\nimport { Card, Col, Row, Spin, Typography, Divider, Button, Space } from 'antd';\nimport { ArrowLeftOutlined } from '@ant-design/icons';\nimport React, { useEffect } from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamManagementCard from './TeamManagementCard';\nimport UserProfileCard from './UserProfileCard';\n\nconst { Title, Text } = Typography;\n\n/**\n * 设置页面组件\n *\n * 这是用户的设置主页面，提供用户个人设置、团队管理等功能。\n * 是用户进行各种配置和管理操作的主要入口页面。\n *\n * 页面功能：\n * 1. 用户个人信息设置\n * 2. 团队创建和管理\n * 3. 账户设置和偏好配置\n * 4. 订阅和计费管理\n *\n * 页面结构：\n * - 顶部：页面标题和描述\n * - 左侧：用户个人设置（响应式布局）\n * - 右侧：团队管理设置（响应式布局）\n * - 浮动：全局操作按钮\n *\n * 权限控制：\n * - 需要用户登录才能访问\n * - 由应用级路由守卫处理登录检查\n * - 支持登录状态变化的实时响应\n *\n * 响应式设计：\n * - 移动端：垂直堆叠布局\n * - 桌面端：左右分栏布局\n * - 自适应不同屏幕尺寸\n */\nconst SettingsPage: React.FC = () => {\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户信息和加载状态：\n   * - initialState: 包含用户和团队信息的全局状态\n   * - loading: 全局状态的加载状态\n   */\n  const { initialState, loading } = useModel('@@initialState');\n\n  /**\n   * 返回上一页\n   *\n   * 在UmiJS 4中，history.goBack()方法已被移除，使用history.go(-1)替代。\n   * 如果没有历史记录可以返回，则跳转到仪表盘作为默认页面。\n   */\n  const handleGoBack = () => {\n    // 检查是否有历史记录可以返回\n    if (window.history.length > 1) {\n      history.go(-1);\n    } else {\n      // 如果没有历史记录，跳转到仪表盘\n      history.push('/dashboard');\n    }\n  };\n\n  /**\n   * 加载状态处理\n   *\n   * 当全局状态正在初始化时，显示加载界面。\n   * 这确保了用户在状态加载完成前看到友好的加载提示。\n   */\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载设置信息...</div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {/* 页面主容器 */}\n      <div\n        style={{\n          minHeight: '100vh',\n          background: 'linear-gradient(135deg, #f5f8ff 0%, #e8f4fd 100%)',\n          padding: '24px',\n        }}\n      >\n        {/* 页面标题区域 */}\n        <div style={{ marginBottom: 24 }}>\n          {/* 返回按钮 */}\n          <div style={{ marginBottom: 16 }}>\n            <Button\n              type=\"text\"\n              icon={<ArrowLeftOutlined />}\n              onClick={handleGoBack}\n              style={{\n                fontSize: '16px',\n                color: '#1890ff',\n                padding: '4px 8px',\n              }}\n            >\n              返回\n            </Button>\n          </div>\n\n          {/* 标题 */}\n          <div style={{ textAlign: 'center' }}>\n            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n              设置中心\n            </Title>\n            <Text type=\"secondary\" style={{ fontSize: '16px' }}>\n              管理您的个人信息、团队设置和账户偏好\n            </Text>\n          </div>\n        </div>\n\n        <Card\n          style={{\n            maxWidth: 1400,\n            margin: '0 auto',\n            borderRadius: 16,\n            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',\n            border: 'none',\n            background: 'rgba(255, 255, 255, 0.95)',\n            backdropFilter: 'blur(10px)',\n          }}\n          bodyStyle={{\n            padding: '32px',\n          }}\n        >\n          {/*\n           * 响应式网格布局\n           *\n           * 使用Ant Design的Row/Col组件实现响应式布局：\n           * - 移动端：垂直堆叠，所有组件占满宽度\n           * - 桌面端：个人设置和团队管理左右分栏\n           * - gutter: 组件间距设置\n           * - margin: 0: 避免Row组件的默认负边距影响布局\n           */}\n          <Row gutter={[24, 24]} style={{ margin: 0 }}>\n            {/*\n             * 用户个人设置区域\n             *\n             * 显示用户的个人信息设置、账户配置等。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据左半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n              style={{ marginBottom: 8 }}\n            >\n              <UserProfileCard />\n            </Col>\n\n            {/*\n             * 团队管理设置区域\n             *\n             * 显示团队创建、团队管理等功能。\n             * 这是团队管理功能的主要入口。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据右半部分\n             */}\n            <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>\n              <TeamManagementCard />\n            </Col>\n          </Row>\n        </Card>\n      </div>\n\n      {/* 全局浮动操作按钮 */}\n      <UserFloatButton />\n    </>\n  );\n};\n\nexport default SettingsPage;\n"], "names": [], "mappings": ";;;;;;;4BAyiBA;;;eAAA;;;;;;4BAziBkC;6BAgB3B;8BAQA;wEACoC;iCACF;;;;;;;;;;AAGzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAE1B;;;;;;;;;;;;;;CAcC,GACD,MAAM,qBAA+B;;IACnC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;IAC/B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA4B;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAA2B,CAAC;IACpF,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAEnD;;;;GAIC,GACD,IAAA,gBAAS,EAAC;QACR,IAAI,yBAAA,mCAAA,aAAc,WAAW,EAC3B;IAEJ,GAAG;QAAC,yBAAA,mCAAA,aAAc,WAAW;KAAC;IAE9B;;GAEC,GACD,MAAM,aAAa;QACjB,gBAAgB;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,qBAAW,CAAC,qBAAqB;YACzD,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA;;;;;;;;;GASC,GACD,MAAM,mBAAmB,OAAO;QAC9B,iBAAiB;QACjB,IAAI;YACF,MAAM,qBAAW,CAAC,UAAU,CAAC;YAE7B,WAAW;YACX,MAAM;YAEN,aAAa;YACb,sBAAsB;YACtB,KAAK,WAAW;YAEhB,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,iBAAiB;YACjB,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,SAAS,cAAc,CAAC;YACtB,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;QAC/B;QACA,oBAAoB;IACtB;IAEA;;GAEC,GACD,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,aAAa;QAElB,eAAe;QACf,IAAI;YACF,WAAW;YACX,MAAM,qBAAW,CAAC,UAAU,CAAC;gBAAE,QAAQ,YAAY,EAAE;YAAC;YAEtD,SAAS;YACT,MAAM,qBAAW,CAAC,iBAAiB,CAAC;gBAClC,MAAM,OAAO,IAAI;gBACjB,aAAa,OAAO,WAAW;YACjC;YAEA,WAAW;YACX,MAAM;YAEN,QAAQ;YACR,oBAAoB;YACpB,eAAe;YACf,SAAS,WAAW;YAEpB,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAO;QAC9B,oBAAoB,CAAA,OAAS,CAAA;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE,CAAC,EAAE;YAAK,CAAA;QACxD,IAAI;YACF,MAAM,qBAAW,CAAC,UAAU,CAAC,KAAK,EAAE;YAEpC,WAAW;YACX,MAAM;YAEN,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,oBAAoB,CAAA,OAAS,CAAA;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE,CAAC,EAAE;gBAAM,CAAA;QAC3D;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,OAAO;QAC7B,oBAAoB,CAAA,OAAS,CAAA;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE,CAAC,EAAE;YAAK,CAAA;QACxD,IAAI;YACF,MAAM,qBAAW,CAAC,SAAS;YAE3B,WAAW;YACX,MAAM;YAEN,gBAAgB;YAChB,IAAI,iBACF,MAAM,gBAAgB,CAAC,YAAe,CAAA;oBACpC,GAAG,SAAS;oBACZ,aAAa;gBACf,CAAA;YAGF,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,oBAAoB,CAAA,OAAS,CAAA;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE,CAAC,EAAE;gBAAM,CAAA;QAC3D;IACF;IAIA,qBACE;;0BACE,2BAAC,UAAI;gBACH,WAAU;gBACV,OAAO;oBACL,cAAc;oBACd,WAAW;oBACX,QAAQ;oBACR,YAAY;oBACZ,QAAQ;gBACV;gBACA,qBACE,2BAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,YAAY;wBAAU,KAAK;oBAAG;;sCAC3D,2BAAC,mBAAY;4BACX,OAAO;gCACL,UAAU;gCACV,OAAO;gCACP,SAAS;gCACT,iBAAiB;gCACjB,cAAc;4BAChB;;;;;;sCAEF,2BAAC;;8CACC,2BAAC;oCACC,OAAO;oCACP,OAAO;wCACL,QAAQ;wCACR,YAAY;wCACZ,sBAAsB;wCACtB,qBAAqB;wCACrB,YAAY;oCACd;8CACD;;;;;;8CAGD,2BAAC;oCAAK,MAAK;oCAAY,OAAO;wCAAE,UAAU;oCAAO;8CAAG;;;;;;;;;;;;;;;;;;;kCAQ1D,2BAAC;wBAAI,OAAO;4BAAE,cAAc;wBAAG;kCAC7B,cAAA,2BAAC,YAAM;4BACL,MAAK;4BACL,oBAAM,2BAAC,mBAAY;;;;;4BACnB,SAAS,IAAM,sBAAsB;4BACrC,MAAK;4BACL,KAAK;4BACL,OAAO;gCACL,cAAc;gCACd,YAAY;gCACZ,QAAQ;gCACR,WAAW;gCACX,QAAQ;gCACR,UAAU;gCACV,YAAY;4BACd;sCACD;;;;;;;;;;;kCAKH,2BAAC,aAAO;wBAAC,OAAO;4BAAE,QAAQ;wBAAS;kCACjC,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;kCAIzB,2BAAC,UAAI;wBACH,SAAS;wBACT,YAAY;wBACZ,YAAY,CAAC;4BACX,MAAM,iBAAiB,CAAC;gCACtB,IAAI,KAAK,SAAS,EAChB,OAAO;oCACL;wCACE,KAAK;wCACL,OAAO;wCACP,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS,IAAM,eAAe;oCAChC;oCACA;wCACE,KAAK;wCACL,qBACE,2BAAC,gBAAU;4CACT,OAAM;4CACN,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC;4CAC9C,WAAW,IAAM,iBAAiB;4CAClC,QAAO;4CACP,YAAW;4CACX,eAAe;gDAAE,QAAQ;4CAAK;sDAE9B,cAAA,2BAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;wCAGvC,oBAAM,2BAAC,qBAAc;;;;;wCACrB,QAAQ;oCACV;iCACD;qCAED,OAAO;oCACL;wCACE,KAAK;wCACL,qBACE,2BAAC,gBAAU;4CACT,OAAM;4CACN,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC;4CACtC,WAAW,IAAM,gBAAgB;4CACjC,QAAO;4CACP,YAAW;4CACX,eAAe;gDAAE,QAAQ;4CAAK;sDAE9B,cAAA,2BAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;wCAGvC,oBAAM,2BAAC,qBAAc;;;;;wCACrB,QAAQ;oCACV;iCACD;4BAEL;4BAEA,qBACE,2BAAC,UAAI,CAAC,IAAI;gCACR,OAAO;oCACL,SAAS;oCACT,cAAc;gCAChB;gCACA,SAAS;kDACP,2BAAC,cAAQ;wCAEP,MAAM;4CACJ,OAAO,eAAe;wCACxB;wCACA,SAAS;4CAAC;yCAAQ;wCAClB,WAAU;kDAEV,cAAA,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,mBAAY;;;;;4CACnB,SAAS,gBAAgB,CAAC,KAAK,EAAE,CAAC;4CAClC,OAAO;gDAAE,UAAU;4CAAO;;;;;;uCAXxB;;;;;iCAcP;0CAED,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;oCACb,sBACE,2BAAC;wCACC,OAAO;4CACL,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,YAAY;4CACZ,SAAS;4CACT,YAAY;4CACZ,gBAAgB;4CAChB,OAAO;4CACP,YAAY;4CACZ,UAAU;wCACZ;kDAEC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;oCAGpC,qBACE,2BAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;wCAAE;;0DAC1D,2BAAC;gDAAK,MAAM;gDAAC,OAAO;oDAAE,UAAU;gDAAO;0DACpC,KAAK,IAAI;;;;;;4CAEX,KAAK,SAAS,kBACb,2BAAC,SAAG;gDAAC,OAAM;gDAAO,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;;;;;;;oCAMrD,2BACE,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAO;;4CAC9C,KAAK,WAAW;4CAAC;;;;;;;;;;;;;;;;;wBAM9B;wBACA,QAAQ;4BAAE,WAAW;wBAAmB;;;;;;;;;;;;0BAO5C,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,oBAAoB;oBACpB,eAAe;oBACf,SAAS,WAAW;gBACtB;gBACA,QAAQ;gBACR,OAAO;gBACP,OAAO;oBAAE,KAAK;gBAAI;0BAElB,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;oBACV,cAAa;oBACb,OAAO;wBAAE,WAAW;oBAAG;;sCAEvB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAW;gCACtC;oCAAE,KAAK;oCAAK,SAAS;gCAAmB;gCACxC;oCAAE,KAAK;oCAAG,SAAS;gCAAgB;6BACpC;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;gCAAU,MAAK;;;;;;;;;;;sCAGpC,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCAAC;oCAAE,KAAK;oCAAK,SAAS;gCAAmB;6BAAE;sCAElD,cAAA,2BAAC;gCACC,aAAY;gCACZ,MAAM;gCACN,SAAS;gCACT,WAAW;;;;;;;;;;;sCAIf,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,SAAS;4CACP,oBAAoB;4CACpB,eAAe;4CACf,SAAS,WAAW;wCACtB;kDACD;;;;;;kDAGD,2BAAC,YAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,oBAAM,2BAAC,mBAAY;;;;;kDACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,2BAAC,WAAK;gBACJ,qBACE,2BAAC,UAAI;oBAAC,OAAM;oBAAS,KAAK;;sCACxB,2BAAC,mBAAY;4BAAC,OAAO;gCAAE,OAAO;4BAAU;;;;;;sCACxC,2BAAC;sCAAK;;;;;;;;;;;;gBAGV,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,KAAK,WAAW;gBAClB;gBACA,QAAQ;gBACR,OAAO;gBACP,OAAO;oBAAE,KAAK;gBAAI;0BAElB,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;oBACV,cAAa;oBACb,OAAO;wBAAE,WAAW;oBAAG;;sCAEvB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAW;gCACtC;oCAAE,KAAK;oCAAK,SAAS;gCAAmB;gCACxC;oCAAE,KAAK;oCAAG,SAAS;gCAAgB;6BACpC;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;gCAAU,MAAK;;;;;;;;;;;sCAGpC,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCAAC;oCAAE,KAAK;oCAAK,SAAS;gCAAmB;6BAAE;sCAElD,cAAA,2BAAC;gCACC,aAAY;gCACZ,MAAM;gCACN,SAAS;gCACT,WAAW;;;;;;;;;;;sCAIf,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAG;sCACjD,cAAA,2BAAC,UAAI;gCAAC,SAAQ;gCAAM,KAAK;;kDACvB,2BAAC,YAAM;wCACL,SAAS;4CACP,sBAAsB;4CACtB,KAAK,WAAW;wCAClB;kDACD;;;;;;kDAGD,2BAAC,YAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,OAAO;4CACL,YAAY;4CACZ,QAAQ;wCACV;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAxfM;;QACW,UAAI,CAAC;QACD,UAAI,CAAC;QASkB,aAAQ;;;KAX9C;IA0fN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC5Uf;;;eAAA;;;;;;4BA7NyB;6BAWlB;8BAMA;wEACoC;iCACf;;;;;;;;;;AAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC;;;;;;;;;;;;;;CAcC,GACD,MAAM,kBAA4B;;IAChC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAEnD;;;;GAIC,GACD,IAAA,gBAAS,EAAC;QACR,IAAI,yBAAA,mCAAA,aAAc,WAAW,EAC3B,KAAK,cAAc,CAAC;YAClB,MAAM,aAAa,WAAW,CAAC,IAAI;YACnC,OAAO,aAAa,WAAW,CAAC,KAAK;YACrC,WAAW,aAAa,WAAW,CAAC,SAAS,IAAI;QACnD;IAEJ,GAAG;QAAC,yBAAA,mCAAA,aAAc,WAAW;QAAE;KAAK;IAEpC;;;;;;;;GAQC,GACD,MAAM,oBAAoB,OAAO;QAC/B,WAAW;QACX,IAAI;YACF,cAAc;YACd,MAAM,cAAc,MAAM,qBAAW,CAAC,aAAa,CAAC;YAEpD,eAAe;YACf,MAAM,gBAAgB,CAAC,YAAe,CAAA;oBACpC,GAAG,SAAS;oBACZ,aAAa;gBACf,CAAA;YAEA,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,iBAAiB;YACjB,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA;;;;GAIC,GACD,MAAM,gBAAgB;QACpB,MAAM,OAAO,yBAAA,mCAAA,aAAc,WAAW;QACtC,IAAI,CAAC,MAAM,OAAO;QAElB,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAC3C,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK;IAC3D;IAEA,qBACE,2BAAC,UAAI;QACH,WAAU;QACV,OAAO;YACL,cAAc;YACd,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,QAAQ;QACV;QACA,qBACE,2BAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,KAAK;YAAG;;8BAC3D,2BAAC,YAAM;oBACL,MAAM;oBACN,OAAO;wBACL,iBAAiB;wBACjB,UAAU;wBACV,YAAY;oBACd;oBACA,oBAAM,2BAAC,mBAAY;;;;;8BAElB;;;;;;8BAEH,2BAAC;;sCACC,2BAAC;4BACC,OAAO;4BACP,OAAO;gCACL,QAAQ;gCACR,YAAY;gCACZ,sBAAsB;gCACtB,qBAAqB;gCACrB,YAAY;4BACd;sCACD;;;;;;sCAGD,2BAAC;4BAAK,MAAK;4BAAY,OAAO;gCAAE,UAAU;4BAAO;sCAAG;;;;;;;;;;;;;;;;;;kBAO1D,cAAA,2BAAC,UAAI;YACH,MAAM;YACN,QAAO;YACP,UAAU;YACV,cAAa;YACb,OAAO;gBAAE,WAAW;YAAG;;8BAEvB,2BAAC,UAAI,CAAC,IAAI;oBACR,OAAM;oBACN,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC;4BAAE,KAAK;4BAAK,SAAS;wBAAgB;wBACrC;4BAAE,KAAK;4BAAG,SAAS;wBAAc;qBAClC;8BAED,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,mBAAY;4BAAC,OAAO;gCAAE,OAAO;4BAAU;;;;;;wBAChD,aAAY;wBACZ,MAAK;;;;;;;;;;;8BAIT,2BAAC,UAAI,CAAC,IAAI;oBAAC,OAAM;oBAAO,MAAK;8BAC3B,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,mBAAY;4BAAC,OAAO;gCAAE,OAAO;4BAAU;;;;;;wBAChD,QAAQ;wBACR,aAAY;wBACZ,MAAK;wBACL,OAAO;4BAAE,iBAAiB;wBAAU;;;;;;;;;;;8BAIxC,2BAAC,UAAI,CAAC,IAAI;oBACR,OAAM;oBACN,MAAK;oBACL,OAAO;wBACL;4BAAE,KAAK;4BAAI,SAAS;wBAAgB;wBACpC;4BACE,SAAS;4BACT,SAAS;wBACX;qBACD;8BAED,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,oBAAa;4BAAC,OAAO;gCAAE,OAAO;4BAAU;;;;;;wBACjD,aAAY;wBACZ,MAAK;;;;;;;;;;;8BAIT,2BAAC,aAAO;oBAAC,OAAO;wBAAE,QAAQ;oBAAS;;;;;;8BAEnC,2BAAC,UAAI,CAAC,IAAI;oBAAC,OAAO;wBAAE,cAAc;wBAAG,WAAW;oBAAQ;8BACtD,cAAA,2BAAC,YAAM;wBACL,MAAK;wBACL,UAAS;wBACT,SAAS;wBACT,oBAAM,2BAAC,mBAAY;;;;;wBACnB,MAAK;wBACL,OAAO;4BACL,cAAc;4BACd,YAAY;4BACZ,QAAQ;4BACR,WAAW;4BACX,UAAU;wBACZ;kCACD;;;;;;;;;;;;;;;;;;;;;;AAOX;GApLM;;QACW,UAAI,CAAC;QAEsB,aAAQ;;;KAH9C;IAsLN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC7Bf;;;eAAA;;;;;;;4BAhMkC;6BACuC;8BACvC;uEACD;6EACL;oFACG;iFACH;;;;;;;;;;AAE5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GACD,MAAM,eAAyB;;IAC7B;;;;;;GAMC,GACD,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,IAAA,aAAQ,EAAC;IAE3C;;;;;GAKC,GACD,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI,OAAO,OAAO,CAAC,MAAM,GAAG,GAC1B,YAAO,CAAC,EAAE,CAAC;aAEX,kBAAkB;QAClB,YAAO,CAAC,IAAI,CAAC;IAEjB;IAEA;;;;;GAKC,GACD,IAAI,SACF,qBACE,2BAAC;QACC,OAAO;YACL,WAAW;YACX,YAAY;YACZ,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;;0BAEA,2BAAC,UAAI;gBAAC,MAAK;;;;;;0BACX,2BAAC;gBAAI,OAAO;oBAAE,YAAY;gBAAG;0BAAG;;;;;;;;;;;;IAKtC,qBACE;;0BAEE,2BAAC;gBACC,OAAO;oBACL,WAAW;oBACX,YAAY;oBACZ,SAAS;gBACX;;kCAGA,2BAAC;wBAAI,OAAO;4BAAE,cAAc;wBAAG;;0CAE7B,2BAAC;gCAAI,OAAO;oCAAE,cAAc;gCAAG;0CAC7B,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,oBAAM,2BAAC,wBAAiB;;;;;oCACxB,SAAS;oCACT,OAAO;wCACL,UAAU;wCACV,OAAO;wCACP,SAAS;oCACX;8CACD;;;;;;;;;;;0CAMH,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAS;;kDAChC,2BAAC;wCAAM,OAAO;wCAAG,OAAO;4CAAE,QAAQ;4CAAG,OAAO;wCAAU;kDAAG;;;;;;kDAGzD,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAO;kDAAG;;;;;;;;;;;;;;;;;;kCAMxD,2BAAC,UAAI;wBACH,OAAO;4BACL,UAAU;4BACV,QAAQ;4BACR,cAAc;4BACd,WAAW;4BACX,QAAQ;4BACR,YAAY;4BACZ,gBAAgB;wBAClB;wBACA,WAAW;4BACT,SAAS;wBACX;kCAWA,cAAA,2BAAC,SAAG;4BAAC,QAAQ;gCAAC;gCAAI;6BAAG;4BAAE,OAAO;gCAAE,QAAQ;4BAAE;;8CASxC,2BAAC,SAAG;oCACF,IAAI;oCACJ,IAAI;oCACJ,IAAI;oCACJ,IAAI;oCACJ,IAAI;oCACJ,KAAK;oCACL,OAAO;wCAAE,cAAc;oCAAE;8CAEzB,cAAA,2BAAC,wBAAe;;;;;;;;;;8CAYlB,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;oCAAI,IAAI;oCAAI,IAAI;oCAAI,KAAK;8CAChD,cAAA,2BAAC,2BAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,2BAAC,oBAAe;;;;;;;AAGtB;GAxJM;;QAQ8B,aAAQ;;;KARtC;IA0JN,WAAe"}