/**
 * 团队设置组件
 * 
 * 功能特性：
 * - 编辑/修改团队名称和描述
 * - 删除整个团队
 * - 团队基本信息管理
 * - 危险操作确认
 * 
 * 权限控制：
 * - 只有团队创建者可以进行设置操作
 * - 删除团队需要二次确认
 */

import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Modal,
  Alert,
  message,
  Popconfirm
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  ExclamationCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { history } from '@umijs/max';

// 导入服务和类型
import { TeamService } from '@/services/team';
import type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface TeamSettingsProps {
  teamDetail: TeamDetailResponse;
  onRefresh: () => void;
}

const TeamSettings: React.FC<TeamSettingsProps> = ({
  teamDetail,
  onRefresh
}) => {
  const [editMode, setEditMode] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');
  const [deleting, setDeleting] = useState(false);
  const [form] = Form.useForm();

  // 进入编辑模式
  const handleEdit = () => {
    form.setFieldsValue({
      name: teamDetail.name,
      description: teamDetail.description || '',
    });
    setEditMode(true);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditMode(false);
    form.resetFields();
  };

  // 保存团队信息
  const handleSaveTeam = async (values: UpdateTeamRequest) => {
    try {
      setUpdating(true);
      await TeamService.updateCurrentTeam(values);
      message.success('团队信息更新成功');
      setEditMode(false);
      onRefresh();
    } catch (error) {
      console.error('更新团队失败:', error);
      message.error('更新团队失败');
    } finally {
      setUpdating(false);
    }
  };

  // 删除团队
  const handleDeleteTeam = async () => {
    if (deleteConfirmText !== teamDetail.name) {
      message.error('请输入正确的团队名称');
      return;
    }

    try {
      setDeleting(true);
      await TeamService.deleteCurrentTeam();
      message.success('团队已删除');
      setDeleteModalVisible(false);
      // 删除成功后跳转到团队选择页面
      history.push('/user/team-select');
    } catch (error) {
      console.error('删除团队失败:', error);
      message.error('删除团队失败');
    } finally {
      setDeleting(false);
    }
  };

  return (
    <div>
      {/* 团队信息编辑 */}
      <Card 
        title="团队信息"
        extra={
          !editMode && (
            <Button 
              type="primary" 
              icon={<EditOutlined />}
              onClick={handleEdit}
            >
              编辑
            </Button>
          )
        }
        style={{ marginBottom: 24 }}
      >
        {editMode ? (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSaveTeam}
          >
            <Form.Item
              name="name"
              label="团队名称"
              rules={[
                { required: true, message: '请输入团队名称' },
                { min: 2, max: 50, message: '团队名称长度应在2-50个字符之间' },
              ]}
            >
              <Input placeholder="请输入团队名称" />
            </Form.Item>
            <Form.Item
              name="description"
              label="团队描述"
              rules={[
                { max: 200, message: '团队描述不能超过200个字符' },
              ]}
            >
              <TextArea 
                rows={4} 
                placeholder="请输入团队描述（可选）"
                showCount
                maxLength={200}
              />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={updating}
                  icon={<SaveOutlined />}
                >
                  保存
                </Button>
                <Button onClick={handleCancelEdit}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        ) : (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Text strong>团队名称：</Text>
              <Title level={4} style={{ margin: '8px 0' }}>{teamDetail.name}</Title>
            </div>
            <div>
              <Text strong>团队描述：</Text>
              <Paragraph style={{ marginTop: 8 }}>
                {teamDetail.description || '暂无描述'}
              </Paragraph>
            </div>
          </div>
        )}
      </Card>

      {/* 危险操作区域 */}
      <Card 
        title={
          <Space>
            <WarningOutlined style={{ color: '#ff4d4f' }} />
            <Text type="danger">危险操作</Text>
          </Space>
        }
      >
        <Alert
          message="删除团队"
          description="删除团队将永久移除所有团队数据，包括成员关系、设置等。此操作不可恢复，请谨慎操作。"
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Popconfirm
          title="确认删除团队"
          description="您确定要删除这个团队吗？此操作不可恢复。"
          onConfirm={() => setDeleteModalVisible(true)}
          okText="确认"
          cancelText="取消"
          okType="danger"
        >
          <Button 
            danger 
            icon={<DeleteOutlined />}
            size="large"
          >
            删除团队
          </Button>
        </Popconfirm>
      </Card>

      {/* 删除确认弹窗 */}
      <Modal
        title={
          <Space>
            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
            <Text type="danger">删除团队确认</Text>
          </Space>
        }
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setDeleteConfirmText('');
        }}
        footer={null}
        width={600}
      >
        <Alert
          message="警告：此操作不可恢复"
          description={
            <div>
              <p>删除团队将会：</p>
              <ul>
                <li>永久删除团队及所有相关数据</li>
                <li>移除所有团队成员</li>
                <li>清除团队设置和配置</li>
                <li>无法恢复任何数据</li>
              </ul>
            </div>
          }
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <div style={{ marginBottom: 16 }}>
          <Text strong>
            请输入团队名称 "<Text code>{teamDetail.name}</Text>" 来确认删除：
          </Text>
        </div>

        <Input
          placeholder={`请输入：${teamDetail.name}`}
          value={deleteConfirmText}
          onChange={(e) => setDeleteConfirmText(e.target.value)}
          style={{ marginBottom: 24 }}
        />

        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button 
              onClick={() => {
                setDeleteModalVisible(false);
                setDeleteConfirmText('');
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              danger
              loading={deleting}
              disabled={deleteConfirmText !== teamDetail.name}
              onClick={handleDeleteTeam}
              icon={<DeleteOutlined />}
            >
              确认删除团队
            </Button>
          </Space>
        </div>
      </Modal>
    </div>
  );
};

export default TeamSettings;
